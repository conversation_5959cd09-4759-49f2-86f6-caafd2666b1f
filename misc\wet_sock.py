# you get an incrementing amount of money for every hour you have wet socks on
# the first hour gives you £20, then £20 more every hour. 
# (£40 hour 2, £60 hour 3, etc.) so you will have £60 after 2 hours, £120 after 3 hours, etc.

INCREMENT_AMOUNT = 50

def target_total(target):
    # returns the number of hours needed to get target amount of money
    total = 0
    hours = 0
    while total < target:
        hours += 1
        total += INCREMENT_AMOUNT * hours
    return hours

def total_after_hours(hours):
    # returns the total amount of money after hours
    total = 0
    for i in range(1, hours + 1):
        total += INCREMENT_AMOUNT * i
    return total

def main():
    target = int(input("Enter target amount: "))
    print(f"Number of hours needed: {target_total(target)}")

if __name__ == "__main__":
    main()