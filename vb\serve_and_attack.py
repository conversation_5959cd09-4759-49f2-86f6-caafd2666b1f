#!/usr/bin/env python3
"""
Volleyball Serve and Attack Simulator

This program generates random serve information like random_serve.py,
and if the serve grade is not "error", it continues with an attack
using attack codes from attack_code_trainer.py.
"""

import random

class ServeAndAttackSimulator:
    def __init__(self):
        # Serve options (from random_serve.py)
        self.sides = ["home", "away"]
        self.serve_types = ["jump serve", "jump float", "standing"]
        self.grades = ["perfect", "good", "ok", "bad", "overpass", "error"]
        
        # Attack codes (from attack_code_trainer.py)
        self.attack_codes = {
            "ca": "foot behind setter",
            "cb": "middle slide",
            "cf": "foot in front of setter",
            "co": "foot by back oppo",
            "g2": "2ball",
            "gb": "back quick",
            "gg": "gap",
            "gt": "tight",
            "i6": "bic",
            "ia": "back 5",
            "ib": "back 5/6",
            "ic": "back 1/6",
            "id": "back 1",
            "p2": "inside red",
            "pr": "red",
            "p4": "inside go",
            "pg": "go",
            "po": "overpass",
            "ps": "2nd ball",
            "v2": "high 2",
            "v4": "high 4",
            "vd": "high d",
            "vp": "high pipe",
            "x2": "front combo",
            "x3": "back combo"
        }
        
        # Attack outcomes
        self.attack_outcomes = ["kill", "continue", "error"]
        
        # Create a list of attack codes for random selection
        self.attack_codes_list = list(self.attack_codes.keys())
    
    def generate_serve(self):
        """Generate random serve information."""
        side = random.choice(self.sides)
        number1 = random.randint(1, 99)
        serve_type = random.choice(self.serve_types)
        number2 = random.randint(1, 99)
        grade = random.choice(self.grades)
        
        return {
            'side': side,
            'number1': number1,
            'serve_type': serve_type,
            'number2': number2,
            'grade': grade
        }
    
    def generate_attack(self):
        """Generate random attack information."""
        attack_code = random.choice(self.attack_codes_list)
        attack_description = self.attack_codes[attack_code]
        outcome = random.choice(self.attack_outcomes)
        
        return {
            'code': attack_code,
            'description': attack_description,
            'outcome': outcome
        }
    
    def run_simulation(self, num_scenarios=10):
        """Run the serve and attack simulation."""
        print("=" * 60)
        print("VOLLEYBALL SERVE AND ATTACK SIMULATOR".center(60))
        print("=" * 60)
        print()
        
        for i in range(num_scenarios):
            print(f"Scenario {i + 1}:")
            print("-" * 20)
            
            # Generate serve
            serve = self.generate_serve()
            serve_info = f"{serve['side']}, {serve['number1']}, {serve['serve_type']}, {serve['number2']}, {serve['grade']}"
            print(f"Serve: {serve_info}")
            
            # Check if serve grade is error
            if serve['grade'] == 'error':
                print("Result: Serve error - rally ends")
                print()
                continue
            
            # Generate attack if serve is not error
            attack = self.generate_attack()
            print(f"Attack: {attack['code']} ({attack['description']}) -> {attack['outcome']}")
            
            # Determine final result
            if attack['outcome'] == 'kill':
                print("Result: Attack kill - rally ends")
            elif attack['outcome'] == 'error':
                print("Result: Attack error - rally ends")
            else:  # continue
                print("Result: Rally continues")
            
            print()

def main():
    """Main function to run the simulator."""
    simulator = ServeAndAttackSimulator()
    
    # Ask user how many scenarios to run
    try:
        num_scenarios = input("How many scenarios would you like to simulate? (default: 10): ").strip()
        if num_scenarios == "":
            num_scenarios = 10
        else:
            num_scenarios = int(num_scenarios)
            if num_scenarios <= 0:
                print("Invalid number. Using default of 10.")
                num_scenarios = 10
    except ValueError:
        print("Invalid input. Using default of 10.")
        num_scenarios = 10
    
    simulator.run_simulation(num_scenarios)

if __name__ == "__main__":
    main()
