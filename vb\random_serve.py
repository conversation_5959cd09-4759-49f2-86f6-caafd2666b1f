import random

# Define options
sides = ["home", "away"]
serve_types = ["jump serve", "jump float", "standing"]
grades = ["perfect", "good", "ok", "bad", "overpass", "error"]

# Generate a few outcomes
for _ in range(10):
    side = random.choice(sides)
    number1 = random.randint(1, 99)
    serve_type = random.choice(serve_types)
    number2 = random.randint(1, 99)
    grade = random.choice(grades)
    
    print(f"{side}, {number1}, {serve_type}, {number2}, {grade}")